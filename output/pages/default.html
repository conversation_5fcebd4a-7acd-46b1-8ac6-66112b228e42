<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招标采购平台 - 首页</title>
    <!-- CSS文件已在主页面(index.html)中统一加载，无需在模板中重复引入 -->
    <style>
        .welcome-container {
            padding: 40px;
            text-align: center;
            background: var(--funi-background-color-light);
            border-radius: 8px;
            margin: 20px;
        }

        .welcome-title {
            font-size: 32px;
            color: var(--funi-primary-color);
            margin-bottom: 20px;
            font-weight: 600;
        }

        .welcome-subtitle {
            font-size: 18px;
            color: var(--funi-text-color-regular);
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .feature-card {
            background: var(--funi-background-color-light);
            border: 1px solid var(--funi-border-color-light);
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            border-color: var(--funi-primary-color);
            box-shadow: 0 4px 12px rgba(0, 127, 255, 0.1);
            transform: translateY(-2px);
        }

        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }

        .feature-title {
            font-size: 20px;
            color: var(--funi-text-color-primary);
            margin-bottom: 15px;
            font-weight: 600;
        }

        .feature-description {
            color: var(--funi-text-color-regular);
            line-height: 1.6;
            font-size: 14px;
        }

        .quick-actions {
            margin-top: 40px;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .quick-action-btn {
            padding: 12px 24px;
            background: var(--funi-primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .quick-action-btn:hover {
            background: var(--funi-primary-dark-color);
            transform: translateY(-1px);
        }

        .quick-action-btn.secondary {
            background: transparent;
            color: var(--funi-primary-color);
            border: 1px solid var(--funi-primary-color);
        }

        .quick-action-btn.secondary:hover {
            background: var(--funi-primary-color);
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .stat-card {
            background: var(--funi-background-color-light);
            border: 1px solid var(--funi-border-color-light);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 600;
            color: var(--funi-primary-color);
            margin-bottom: 8px;
        }

        .stat-label {
            color: var(--funi-text-color-regular);
            font-size: 14px;
        }
    </style>
</head>

<body>
    <div id="app" class="welcome-container">
        <div class="welcome-title">🏢 招标采购平台</div>
        <div class="welcome-subtitle">
            欢迎使用招标采购管理系统<br>
            一站式解决方案，管理从初步规划、采购执行、合同履行到数据分析的整个采购生命周期
        </div>

        <!-- 统计数据 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">156</div>
                <div class="stat-label">总项目数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">89</div>
                <div class="stat-label">进行中项目</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">67</div>
                <div class="stat-label">已完成项目</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">12</div>
                <div class="stat-label">待审核项目</div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="quick-actions">
            <a href="#/procurement-plan" class="quick-action-btn">📋 采购计划管理</a>
            <a href="#/procurement-execution" class="quick-action-btn secondary">🚀 采购执行管理</a>
            <a href="#/supplier-management" class="quick-action-btn secondary">🏪 供应商管理</a>
        </div>

        <!-- 功能特性 -->
        <div class="feature-grid">
            <div class="feature-card" onclick="navigateTo('#/procurement-plan')">
                <span class="feature-icon">📋</span>
                <div class="feature-title">采购计划管理</div>
                <div class="feature-description">
                    对采购计划（立项）进行新增、编辑、删除、查询、审批、委派、导入、导出等全生命周期管理
                </div>
            </div>

            <div class="feature-card" onclick="navigateTo('#/procurement-execution')">
                <span class="feature-icon">🚀</span>
                <div class="feature-title">采购执行管理</div>
                <div class="feature-description">
                    项目标段管理、公告管理、补遗澄清答疑、评标结果公示、中标结果公示、签约履行管理
                </div>
            </div>

            <div class="feature-card" onclick="navigateTo('#/supplier-management')">
                <span class="feature-icon">🏪</span>
                <div class="feature-title">供应商管理</div>
                <div class="feature-description">
                    对供应商信息进行全面管理，并提供360度全景视图，展示其历史合作数据
                </div>
            </div>

            <div class="feature-card" onclick="navigateTo('#/project-warning')">
                <span class="feature-icon">⚠️</span>
                <div class="feature-title">项目预警管理</div>
                <div class="feature-description">
                    对系统触发的风险预警进行查看、处理和跟踪，确保项目合规运行
                </div>
            </div>

            <div class="feature-card" onclick="navigateTo('#/data-analysis')">
                <span class="feature-icon">📊</span>
                <div class="feature-title">数据统计分析</div>
                <div class="feature-description">
                    提供交互式仪表盘，进行多维度可视化分析，生成并导出详细的统计报表
                </div>
            </div>

            <div class="feature-card" onclick="navigateTo('#/system-settings')">
                <span class="feature-icon">⚙️</span>
                <div class="feature-title">系统设置</div>
                <div class="feature-description">
                    预警配置、用户权限管理、审批流程管理等系统级配置功能
                </div>
            </div>
        </div>
    </div>

    <script>
        // 导航函数
        function navigateTo(hash) {
            if (window.top && window.top.location) {
                window.top.location.hash = hash;
            } else {
                window.location.hash = hash;
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('默认首页加载完成');
        });
    </script>
</body>

</html>
