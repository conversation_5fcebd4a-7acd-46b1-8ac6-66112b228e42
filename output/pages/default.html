<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招标采购平台 - 首页</title>
    <!-- CSS文件已在主页面(index.html)中统一加载，无需在模板中重复引入 -->
    <style>
        /* 重置和基础样式 */
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }

        .welcome-container {
            padding: 40px;
            text-align: center;
            background: #ffffff;
            border-radius: 12px;
            margin: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            min-height: calc(100vh - 40px);
        }

        .welcome-title {
            font-size: 36px;
            color: #007FFF;
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0, 127, 255, 0.1);
        }

        .welcome-subtitle {
            font-size: 18px;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.8;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            margin-top: 40px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .feature-card {
            background: #ffffff;
            border: 2px solid #e8f4fd;
            border-radius: 12px;
            padding: 32px 24px;
            text-align: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007FFF, #40a9ff);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            border-color: #007FFF;
            box-shadow: 0 8px 24px rgba(0, 127, 255, 0.15);
            transform: translateY(-4px);
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-icon {
            font-size: 52px;
            margin-bottom: 20px;
            display: block;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .feature-title {
            font-size: 20px;
            color: #1a1a1a;
            margin-bottom: 16px;
            font-weight: 600;
        }

        .feature-description {
            color: #666;
            line-height: 1.6;
            font-size: 14px;
        }

        .quick-actions {
            margin-top: 40px;
            display: flex;
            justify-content: center;
            gap: 16px;
            flex-wrap: wrap;
        }

        .quick-action-btn {
            padding: 14px 28px;
            background: linear-gradient(135deg, #007FFF, #40a9ff);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            display: inline-block;
            box-shadow: 0 2px 8px rgba(0, 127, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .quick-action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .quick-action-btn:hover::before {
            left: 100%;
        }

        .quick-action-btn:hover {
            background: linear-gradient(135deg, #0056cc, #007FFF);
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 127, 255, 0.3);
        }

        .quick-action-btn.secondary {
            background: transparent;
            color: #007FFF;
            border: 2px solid #007FFF;
            box-shadow: none;
        }

        .quick-action-btn.secondary:hover {
            background: #007FFF;
            color: white;
            box-shadow: 0 4px 16px rgba(0, 127, 255, 0.3);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
            margin: 40px auto;
            max-width: 1000px;
        }

        .stat-card {
            background: #ffffff;
            border: 2px solid #e8f4fd;
            border-radius: 12px;
            padding: 24px 20px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #007FFF, #40a9ff);
        }

        .stat-card:hover {
            border-color: #007FFF;
            box-shadow: 0 6px 20px rgba(0, 127, 255, 0.1);
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #007FFF;
            margin-bottom: 8px;
            text-shadow: 0 1px 2px rgba(0, 127, 255, 0.1);
        }

        .stat-label {
            color: #666;
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
</head>

<body>
    <div id="app" class="welcome-container">
        <div class="welcome-title">🏢 招标采购平台</div>
        <div class="welcome-subtitle">
            欢迎使用招标采购管理系统<br>
            一站式解决方案，管理从初步规划、采购执行、合同履行到数据分析的整个采购生命周期
        </div>

        <!-- 统计数据 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">156</div>
                <div class="stat-label">总项目数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">89</div>
                <div class="stat-label">进行中项目</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">67</div>
                <div class="stat-label">已完成项目</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">12</div>
                <div class="stat-label">待审核项目</div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="quick-actions">
            <a href="#/procurement-plan" class="quick-action-btn">📋 采购计划管理</a>
            <a href="#/procurement-execution" class="quick-action-btn secondary">🚀 采购执行管理</a>
            <a href="#/supplier-management" class="quick-action-btn secondary">🏪 供应商管理</a>
        </div>

        <!-- 功能特性 -->
        <div class="feature-grid">
            <div class="feature-card" onclick="navigateTo('#/procurement-plan')">
                <span class="feature-icon">📋</span>
                <div class="feature-title">采购计划管理</div>
                <div class="feature-description">
                    对采购计划（立项）进行新增、编辑、删除、查询、审批、委派、导入、导出等全生命周期管理
                </div>
            </div>

            <div class="feature-card" onclick="navigateTo('#/procurement-execution')">
                <span class="feature-icon">🚀</span>
                <div class="feature-title">采购执行管理</div>
                <div class="feature-description">
                    项目标段管理、公告管理、补遗澄清答疑、评标结果公示、中标结果公示、签约履行管理
                </div>
            </div>

            <div class="feature-card" onclick="navigateTo('#/supplier-management')">
                <span class="feature-icon">🏪</span>
                <div class="feature-title">供应商管理</div>
                <div class="feature-description">
                    对供应商信息进行全面管理，并提供360度全景视图，展示其历史合作数据
                </div>
            </div>

            <div class="feature-card" onclick="navigateTo('#/project-warning')">
                <span class="feature-icon">⚠️</span>
                <div class="feature-title">项目预警管理</div>
                <div class="feature-description">
                    对系统触发的风险预警进行查看、处理和跟踪，确保项目合规运行
                </div>
            </div>

            <div class="feature-card" onclick="navigateTo('#/data-analysis')">
                <span class="feature-icon">📊</span>
                <div class="feature-title">数据统计分析</div>
                <div class="feature-description">
                    提供交互式仪表盘，进行多维度可视化分析，生成并导出详细的统计报表
                </div>
            </div>

            <div class="feature-card" onclick="navigateTo('#/system-settings')">
                <span class="feature-icon">⚙️</span>
                <div class="feature-title">系统设置</div>
                <div class="feature-description">
                    预警配置、用户权限管理、审批流程管理等系统级配置功能
                </div>
            </div>
        </div>
    </div>

    <script>
        // 导航函数
        function navigateTo(hash) {
            if (window.top && window.top.location) {
                window.top.location.hash = hash;
            } else {
                window.location.hash = hash;
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('默认首页加载完成');
        });
    </script>
</body>

</html>
