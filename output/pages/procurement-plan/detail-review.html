<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划管理 - 详情/审核</title>
    <!-- CSS文件已在主页面(index.html)中统一加载，无需在模板中重复引入 -->
</head>

<body>

    <div id="app" class="form-container">
        <div class="detail-header">
            <div class="header-left">
                <div class="header-title">采购计划管理 - 详情</div>
            </div>
            <div class="header-right">
                <div class="approval-info">
                    <span>计划编号: <span id="displayPlanNumber">CG—20240808—0001</span></span>
                    <span>审核状态: <span id="displayAuditStatus">待审核</span></span>
                </div>
            </div>
        </div>

        <div class="funi-tabs">
            <div class="tab-item active" data-tab="basic-info">基本信息</div>
            <div class="tab-item" data-tab="operation-log">流程记录</div>
        </div>

        <div class="container-content">
            <div class="form-step-content form-step-1 active" data-tab-content="basic-info">
                <div class="form-section-title">招标信息</div>
                <div class="funi-form">
                    <form class="form-grid">
                        <div class="form-item-row">
                            <label for="planNumber" class="form-item-label">计划编号:</label>
                            <div class="form-item-value">
                                <div id="planNumber" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="planProjectName" class="form-item-label">计划项目名称:</label>
                            <div class="form-item-value">
                                <div id="planProjectName" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="auditStatus" class="form-item-label">审核状态:</label>
                            <div class="form-item-value">
                                <div id="auditStatus" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementType" class="form-item-label">采购类型:</label>
                            <div class="form-item-value">
                                <div id="procurementType" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementMethod" class="form-item-label">采购方式:</label>
                            <div class="form-item-value">
                                <div id="procurementMethod" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="biddingCategory" class="form-item-label">招标类别:</label>
                            <div class="form-item-value">
                                <div id="biddingCategory" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="budgetAmount" class="form-item-label">采购预算金额（万元）:</label>
                            <div class="form-item-value">
                                <div id="budgetAmount" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="fundSource" class="form-item-label">资金来源:</label>
                            <div class="form-item-value">
                                <div id="fundSource" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="biddingTime" class="form-item-label">招标时间:</label>
                            <div class="form-item-value">
                                <div id="biddingTime" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementOrganizationMethod" class="form-item-label">采购组织方式:</label>
                            <div class="form-item-value">
                                <div id="procurementOrganizationMethod" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="agency" class="form-item-label">代理机构:</label>
                            <div class="form-item-value">
                                <div id="agency" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="annualProcurementPlan" class="form-item-label">年采购计划（万元）:</label>
                            <div class="form-item-value">
                                <div id="annualProcurementPlan" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectHandler" class="form-item-label">项目经办人:</label>
                            <div class="form-item-value">
                                <div id="projectHandler" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="decisionDate" class="form-item-label">立项决策日期:</label>
                            <div class="form-item-value">
                                <div id="decisionDate" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="rejectionReason" class="form-item-label">驳回原因:</label>
                            <div class="form-item-value">
                                <div id="rejectionReason" class="display-value"></div>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="form-section-title">项目信息</div>
                <div class="funi-form">
                    <form class="form-grid">
                        <div class="form-item-row">
                            <label for="projectType" class="form-item-label">项目类型:</label>
                            <div class="form-item-value">
                                <div id="projectType" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectOwner" class="form-item-label">项目业主:</label>
                            <div class="form-item-value">
                                <div id="projectOwner" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row form-item-full-width">
                            <label for="projectBasicInfo" class="form-item-label">项目基本情况:</label>
                            <div class="form-item-value">
                                <div id="projectBasicInfo" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="secondaryCompany" class="form-item-label">所属二级公司单位:</label>
                            <div class="form-item-value">
                                <div id="secondaryCompany" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="remarks" class="form-item-label">备注:</label>
                            <div class="form-item-value">
                                <div id="remarks" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row form-item-full-width">
                            <label for="decisionFiles" class="form-item-label">立项决策文件:</label>
                            <div class="form-item-value">
                                <div id="decisionFiles" class="display-value"></div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="form-step-content" data-tab-content="operation-log">
                <div class="form-section-title">流程记录</div>
                <div class="timeline-container">
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">创建计划</div>
                            <div class="timeline-meta">
                                <span>操作人: 张三</span>
                                <span>操作时间: 2024-08-08 10:00:00</span>
                            </div>
                            <div class="timeline-description">采购计划已创建，等待提交审核。</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">提交审核</div>
                            <div class="timeline-meta">
                                <span>操作人: 张三</span>
                                <span>操作时间: 2024-08-08 14:30:00</span>
                            </div>
                            <div class="timeline-description">计划已提交至审批流程。</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">部门初审</div>
                            <div class="timeline-meta">
                                <span>操作人: 李四</span>
                                <span>操作时间: 2024-08-09 09:15:00</span>
                            </div>
                            <div class="timeline-description">初审通过，转交至财务部。</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">财务部审核</div>
                            <div class="timeline-meta">
                                <span>操作人: 王五</span>
                                <span>操作时间: 2024-08-09 15:20:00</span>
                            </div>
                            <div class="timeline-description">财务审核通过，预算充足。</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">最终审批</div>
                            <div class="timeline-meta">
                                <span>操作人: 赵六</span>
                                <span>操作时间: 2024-08-10 11:00:00</span>
                            </div>
                            <div class="timeline-description">审批通过，计划正式生效。</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JS文件已在主页面(index.html)中统一加载，无需在模板中重复引入 -->
    <script>
        // ===== iframe环境兼容的状态管理 =====
        // 使用window对象避免变量重复声明
        window.pageState = window.pageState || {
            isInitialized: false,
            currentTab: 'basic-info',
            currentId: null
        };

        // 页面状态重置函数
        function resetPageState() {
            window.pageState.isInitialized = false;
            window.pageState.currentTab = 'basic-info';
            window.pageState.currentId = null;
        }

        // Tab switching initialization with iframe compatibility
        function initTabSwitching() {
            const tabItems = document.querySelectorAll('.funi-tabs .tab-item');
            const tabContents = document.querySelectorAll('.form-step-content');

            // Check if elements are found, retry if not
            if (tabItems.length === 0 || tabContents.length === 0) {
                setTimeout(initTabSwitching, 100);
                return;
            }

            tabItems.forEach(item => {
                item.addEventListener('click', () => {
                    // Remove active class from all tab items and contents
                    tabItems.forEach(tab => tab.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Add active class to the clicked tab item
                    item.classList.add('active');

                    // Show the corresponding tab content
                    const targetTab = item.dataset.tab;
                    const targetContent = document.querySelector(`[data-tab-content="${targetTab}"]`);
                    if (targetContent) {
                        targetContent.classList.add('active');
                    }
                });
            });
        }

        // Page data initialization with iframe compatibility
        function initPageData() {
            // 每次初始化时重置状态，避免缓存问题
            resetPageState();
            window.pageState.isInitialized = true;

            // 获取URL参数
            const hash = window.top ? window.top.location.hash : window.location.hash;
            const queryString = hash.split('?')[1] || '';
            const urlParams = new URLSearchParams(queryString);
            const id = urlParams.get('id');
            const reviewParam = urlParams.get('review');
            window.pageState.currentId = id;

            if (id) {
                loadPageData(id);
            }

            // 如果是审核模式，添加审核按钮
            if (reviewParam === '1') {
                addReviewButtons();
            }
        }

        // 添加审核按钮
        function addReviewButtons() {
            // 检查是否已存在form-actions，避免重复创建
            const existingFormActions = document.querySelector('.form-actions');
            if (!existingFormActions) {
                const appDiv = document.getElementById('app');
                const formActionsDiv = document.createElement('div');
                formActionsDiv.className = 'form-actions';
                formActionsDiv.innerHTML = `
                    <button class="button primary" onclick="approveAction()">通过</button>
                    <button class="button" onclick="rejectAction()">驳回</button>
                `;
                appDiv.appendChild(formActionsDiv);
            }
        }

        // 审核通过
        window.approveAction = function() {
            if (confirm('确定要审核通过这个采购计划吗？')) {
                alert('审批通过！');
                // 返回列表页
                if (window.top && window.top.location) {
                    window.top.location.hash = '#/procurement-plan';
                }
            }
        };

        // 审核驳回
        window.rejectAction = function() {
            const reason = prompt('请输入驳回原因:');
            if (reason && reason.trim()) {
                alert(`审批驳回，原因: ${reason}`);
                // 返回列表页
                if (window.top && window.top.location) {
                    window.top.location.hash = '#/procurement-plan';
                }
            } else if (reason !== null) {
                alert('请输入驳回原因');
            }
        };

        // 加载页面数据
        function loadPageData(id) {
            // 模拟数据（实际项目中应该通过API获取）
            const dummyData = {
                planNumber: id,
                planProjectName: '2024年度办公用品采购计划',
                auditStatus: '待审核',
                procurementType: '货物',
                procurementMethod: '公告比选',
                biddingCategory: '货物类(材料/设备/供应及安装)',
                budgetAmount: '150.00',
                fundSource: '自有资金',
                biddingTime: '2024年第三季度',
                procurementOrganizationMethod: '自主招标',
                agency: '无',
                annualProcurementPlan: '140.00',
                projectHandler: '张三',
                decisionDate: '2024-08-08',
                rejectionReason: '无',
                projectType: '依法必须招标项目',
                projectOwner: '行政部',
                projectBasicInfo: '本计划旨在采购公司日常运营所需的各类办公用品，以确保各部门工作的顺利进行。采购范围包括但不限于文具、纸张、打印耗材、小型办公设备等。',
                secondaryCompany: '集团本部',
                remarks: '请优先考虑环保型产品。',
                decisionFiles: '立项决策文件.pdf, 附件说明.docx'
            };

            // 更新头部信息
            const displayPlanNumberEl = document.getElementById('displayPlanNumber');
            if (displayPlanNumberEl) displayPlanNumberEl.textContent = dummyData.planNumber;

            const displayAuditStatusEl = document.getElementById('displayAuditStatus');
            if (displayAuditStatusEl) displayAuditStatusEl.textContent = dummyData.auditStatus;

            const headerTitleEl = document.querySelector('.header-title');
            if (headerTitleEl) headerTitleEl.textContent = `采购计划管理 - ${dummyData.planProjectName}`;

            // 填充详情数据
            const fieldMapping = {
                'planNumber': dummyData.planNumber,
                'planProjectName': dummyData.planProjectName,
                'auditStatus': dummyData.auditStatus,
                'procurementType': dummyData.procurementType,
                'procurementMethod': dummyData.procurementMethod,
                'biddingCategory': dummyData.biddingCategory,
                'budgetAmount': dummyData.budgetAmount,
                'fundSource': dummyData.fundSource,
                'biddingTime': dummyData.biddingTime,
                'procurementOrganizationMethod': dummyData.procurementOrganizationMethod,
                'agency': dummyData.agency,
                'annualProcurementPlan': dummyData.annualProcurementPlan,
                'projectHandler': dummyData.projectHandler,
                'decisionDate': dummyData.decisionDate,
                'rejectionReason': dummyData.rejectionReason,
                'projectType': dummyData.projectType,
                'projectOwner': dummyData.projectOwner,
                'projectBasicInfo': dummyData.projectBasicInfo,
                'secondaryCompany': dummyData.secondaryCompany,
                'remarks': dummyData.remarks,
                'decisionFiles': dummyData.decisionFiles
            };

            // 填充所有字段
            Object.keys(fieldMapping).forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.textContent = fieldMapping[fieldId];
                }
            });
        }

        // Initialize functions with iframe compatibility
        document.addEventListener('DOMContentLoaded', function() {
            initTabSwitching();
            initPageData();
        });

        // Also try immediate initialization if DOM is already loaded
        if (document.readyState !== 'loading') {
            initTabSwitching();
            initPageData();
        }

        // 添加延迟初始化以确保iframe环境兼容性
        window.addEventListener('load', function() {
            initTabSwitching();
            initPageData();
        });

        // 超时初始化作为最后的保障
        setTimeout(function() {
            initTabSwitching();
            initPageData();
        }, 100);
    </script>
</body>

</html>
