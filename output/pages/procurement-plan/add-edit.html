<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划管理 - 新增/编辑</title>
    <!-- CSS文件已在主页面(index.html)中统一加载，无需在模板中重复引入 -->
</head>

<body>

    <div id="app" class="form-container">
        <div class="form-step-container">
            <div class="form-step active">
                <div class="step-number">1</div>
                <div class="step-title">招标信息</div>
            </div>
            <iconify-icon icon="ic:outline-navigate-next" class="funi-step-icon"></iconify-icon>
            <div class="form-step">
                <div class="step-number">2</div>
                <div class="step-title">项目信息</div>
            </div>
        </div>
        <div class="container-content">
            <div class="form-step-content form-step-1 active">
                <div class="form-section-title">招标信息</div>
                <div class="funi-form">
                    <form class="form-grid">
                        <div class="form-item-row">
                            <label for="planNumber" class="form-item-label">计划编号:</label>
                            <div class="form-item-value">
                                <input type="text" id="planNumber" name="planNumber" value="自动生成" readonly>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="planProjectName" class="form-item-label required">计划项目名称:</label>
                            <div class="form-item-value">
                                <input type="text" id="planProjectName" name="planProjectName" placeholder="请输入计划项目名称" maxlength="100" required>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementType" class="form-item-label required">采购类型:</label>
                            <div class="form-item-value">
                                <select id="procurementType" name="procurementType" required>
                                    <option value="施工">施工</option>
                                    <option value="货物">货物</option>
                                    <option value="服务">服务</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="biddingCategory" class="form-item-label required">招标类别:</label>
                            <div class="form-item-value">
                                <select id="biddingCategory" name="biddingCategory" required>
                                    <!-- Options will be dynamically loaded based on procurementType -->
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementMethod" class="form-item-label required">采购方式:</label>
                            <div class="form-item-value">
                                <select id="procurementMethod" name="procurementMethod" required>
                                    <option value="公告比选">公告比选</option>
                                    <option value="邀请比选">邀请比选</option>
                                    <option value="竞争性磋商">竞争性磋商</option>
                                    <option value="竞争性谈判">竞争性谈判</option>
                                    <option value="询价择优">询价择优</option>
                                    <option value="单一来源">单一来源</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="budgetAmount" class="form-item-label required">采购预算金额（万元）:</label>
                            <div class="form-item-value">
                                <input type="number" id="budgetAmount" name="budgetAmount" placeholder="请输入采购预算金额" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="fundSource" class="form-item-label required">资金来源:</label>
                            <div class="form-item-value">
                                <select id="fundSource" name="fundSource" required>
                                    <option value="自有资金">自有资金</option>
                                    <option value="政府投资">政府投资</option>
                                    <option value="其它社会资本">其它社会资本</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="biddingTime" class="form-item-label">招标时间:</label>
                            <div class="form-item-value">
                                <input type="text" id="biddingTime" name="biddingTime" placeholder="如：2024年第3季度" maxlength="50">
                                <div class="form-item-tip">示例：2023年7月/2023年1季度</div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementOrganizationMethod" class="form-item-label required">采购组织方式:</label>
                            <div class="form-item-value">
                                <select id="procurementOrganizationMethod" name="procurementOrganizationMethod" required>
                                    <option value="委托招标">委托招标</option>
                                    <option value="自主招标">自主招标</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row" id="agencyRow">
                            <label for="agency" class="form-item-label required">代理机构:</label>
                            <div class="form-item-value">
                                <select id="agency" name="agency" required>
                                    <option value="">请选择代理机构</option>
                                    <option value="代理机构A">代理机构A</option>
                                    <option value="代理机构B">代理机构B</option>
                                    <option value="代理机构C">代理机构C</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="annualProcurementPlan" class="form-item-label">年采购计划（万元）:</label>
                            <div class="form-item-value">
                                <input type="number" id="annualProcurementPlan" name="annualProcurementPlan" value="0.00" step="0.01" min="0">
                                <div class="form-item-tip">不能大于采购预算金额</div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectHandler" class="form-item-label required">项目经办人:</label>
                            <div class="form-item-value">
                                <input type="text" id="projectHandler" name="projectHandler" value="当前创建人" readonly>
                                <div class="form-item-tip">点击后弹窗展示集团CA组织机构及人员信息</div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="decisionDate" class="form-item-label required">立项决策日期:</label>
                            <div class="form-item-value">
                                <input type="date" id="decisionDate" name="decisionDate" required>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="form-step-content form-step-2">
                <div class="form-section-title">项目信息</div>
                <div class="funi-form">
                    <form class="form-grid">
                        <div class="form-item-row">
                            <label for="projectType" class="form-item-label required">项目类型:</label>
                            <div class="form-item-value">
                                <select id="projectType" name="projectType" required>
                                    <option value="依法必须招标项目">依法必须招标项目</option>
                                    <option value="非法定招标采购项目">非法定招标采购项目</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectOwner" class="form-item-label required">项目业主:</label>
                            <div class="form-item-value">
                                <input type="text" id="projectOwner" name="projectOwner" placeholder="由项目经办人信息自动带入，可再次配置" maxlength="50" required>
                            </div>
                        </div>
                        <div class="form-item-row form-item-full-width">
                            <label for="projectBasicInfo" class="form-item-label">项目基本情况:</label>
                            <div class="form-item-value">
                                <textarea id="projectBasicInfo" name="projectBasicInfo" maxlength="255" rows="4" placeholder="请描述项目基本情况"></textarea>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="secondaryCompany" class="form-item-label">所属二级公司单位:</label>
                            <div class="form-item-value">
                                <input type="text" id="secondaryCompany" name="secondaryCompany" maxlength="255" placeholder="请选择二级公司单位" readonly>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="remarks" class="form-item-label">备注:</label>
                            <div class="form-item-value">
                                <input type="text" id="remarks" name="remarks" maxlength="255" placeholder="请输入备注信息">
                            </div>
                        </div>
                        <div class="form-item-row form-item-full-width">
                            <label for="decisionFiles" class="form-item-label">立项决策文件:</label>
                            <div class="form-item-value">
                                <input type="file" id="decisionFiles" name="decisionFiles" multiple accept=".pdf,.doc,.docx,.xls,.xlsx">
                                <div class="form-item-tip">支持多附件上传</div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="form-actions" id="formActions">
            <!-- Buttons will be dynamically loaded here -->
        </div>
    </div>

    <!-- JS文件已在主页面(index.html)中统一加载，无需在模板中重复引入 -->
    <script>
        // ==================== iframe兼容的状态管理 ====================
        // 使用window对象避免变量重复声明
        window.formPageState = window.formPageState || {
            isInitialized: false,
            currentStep: 1,
            totalSteps: 2,
            formSteps: null,
            formStepContents: null,
            formActions: null,
            isStepForm: false
        };

        // 页面状态重置函数
        function resetFormPageState() {
            window.formPageState.isInitialized = false;
            window.formPageState.currentStep = 1;
            window.formPageState.totalSteps = 2;
            window.formPageState.formSteps = null;
            window.formPageState.formStepContents = null;
            window.formPageState.formActions = null;
            window.formPageState.isStepForm = false;
        }

        // 兼容性变量（保持向后兼容）
        let currentStep, totalSteps, formSteps, formStepContents, formActions, isStepForm;

        // ==================== 步骤表单全局函数 ====================

        // 全局函数 - 下一步（兼容iframe环境）
        window.nextStep = function() {
            if (validateCurrentStep() && currentStep < totalSteps) {
                showStep(currentStep + 1);
            }
        };

        // 全局函数 - 上一步（兼容iframe环境）
        window.prevStep = function() {
            if (isStepForm && currentStep > 1) {
                showStep(currentStep - 1);
            }
        };

        // 全局函数 - 显示指定步骤（兼容iframe环境）
        function showStep(stepNumber) {
            if (!isStepForm) return;

            currentStep = stepNumber;

            // 确保DOM元素已加载
            if (!formSteps) {
                formSteps = document.querySelectorAll('.form-step');
                formStepContents = document.querySelectorAll('.form-step-content');
                formActions = document.getElementById('formActions');
            }

            // 更新步骤指示器
            formSteps.forEach((step, index) => {
                if (index + 1 === stepNumber) {
                    step.classList.add('active');
                } else {
                    step.classList.remove('active');
                }
            });

            // 更新步骤内容
            formStepContents.forEach((content, index) => {
                if (index + 1 === stepNumber) {
                    content.classList.add('active');
                } else {
                    content.classList.remove('active');
                }
            });

            updateButtons();
        }

        // 更新按钮（使用innerHTML生成，兼容性更好）
        function updateButtons() {
            if (!isStepForm || !formActions) {
                formActions = document.getElementById('formActions');
            }

            if (!formActions) return;

            let buttonsHtml = '';

            // 上一步按钮
            if (currentStep > 1) {
                buttonsHtml += '<button type="button" class="button" onclick="prevStep()">上一步</button>';
            }

            // 下一步或提交按钮
            if (currentStep < totalSteps) {
                buttonsHtml += '<button type="button" class="button primary" onclick="nextStep()">下一步</button>';
            } else {
                // 最后一步
                buttonsHtml += '<button type="button" class="button" onclick="saveDraft()">保存草稿</button>';
                buttonsHtml += '<button type="button" class="button primary" onclick="submitForm()">提交</button>';
            }

            formActions.innerHTML = buttonsHtml;
        }

        // 保存草稿 - 全局函数
        window.saveDraft = function() {
            alert('草稿保存成功！');
        };

        // 全局函数定义 - 确保HTML onclick可以访问
        window.submitForm = function() {
            if (validateForm()) {
                alert('提交成功！');
                // 返回列表页
                if (window.top && window.top.location) {
                    window.top.location.hash = '#/procurement-plan';
                }
            }
        };

        // 返回列表页 - 全局函数
        window.goBack = function() {
            if (window.top && window.top.location) {
                window.top.location.hash = '#/procurement-plan';
            }
        };

        // 当前步骤验证
        function validateCurrentStep() {
            if (currentStep === 1) {
                // 验证第一步必填项
                const requiredFields = ['planProjectName', 'procurementType', 'biddingCategory', 'procurementMethod', 'budgetAmount', 'fundSource', 'procurementOrganizationMethod', 'projectHandler', 'decisionDate'];
                
                // 如果是委托招标，代理机构也是必填的
                const procurementOrganizationMethod = document.getElementById('procurementOrganizationMethod').value;
                if (procurementOrganizationMethod === '委托招标') {
                    requiredFields.push('agency');
                }
                
                for (let fieldId of requiredFields) {
                    const field = document.getElementById(fieldId);
                    if (!field.value.trim()) {
                        alert(`请填写${field.previousElementSibling.textContent.replace(':', '').replace('※', '')}`);
                        field.focus();
                        return false;
                    }
                }
                
                // 验证年采购计划不能大于预算金额
                const budgetAmount = parseFloat(document.getElementById('budgetAmount').value) || 0;
                const annualPlan = parseFloat(document.getElementById('annualProcurementPlan').value) || 0;
                if (annualPlan > budgetAmount) {
                    alert('年采购计划不能大于采购预算金额');
                    document.getElementById('annualProcurementPlan').focus();
                    return false;
                }
            } else if (currentStep === 2) {
                // 验证第二步必填项
                const requiredFields = ['projectType', 'projectOwner'];
                for (let fieldId of requiredFields) {
                    const field = document.getElementById(fieldId);
                    if (!field.value.trim()) {
                        alert(`请填写${field.previousElementSibling.textContent.replace(':', '').replace('※', '')}`);
                        field.focus();
                        return false;
                    }
                }
            }
            return true;
        }

        // 表单验证
        function validateForm() {
            // 验证所有步骤
            for (let step = 1; step <= totalSteps; step++) {
                const originalStep = currentStep;
                currentStep = step;
                if (!validateCurrentStep()) {
                    showStep(step);
                    return false;
                }
                currentStep = originalStep;
            }
            return true;
        }

        // ==================== 页面初始化函数 ====================
        function initializePage() {
            // 检测是否为步骤表单
            formSteps = document.querySelectorAll('.form-step');
            formStepContents = document.querySelectorAll('.form-step-content');
            formActions = document.getElementById('formActions');

            isStepForm = formSteps.length > 0;

            if (isStepForm) {
                // 步骤表单逻辑
                totalSteps = formSteps.length;

                // 初始化第一步
                showStep(1);

                // 绑定步骤点击事件
                formSteps.forEach((step, index) => {
                    step.addEventListener('click', function() {
                        showStep(index + 1);
                    });
                });
            }

            // ==================== 业务逻辑 ====================
            const procurementTypeSelect = document.getElementById('procurementType');
            const biddingCategorySelect = document.getElementById('biddingCategory');
            const procurementOrganizationMethodSelect = document.getElementById('procurementOrganizationMethod');
            const agencyRow = document.getElementById('agencyRow');
            const decisionDateInput = document.getElementById('decisionDate');

            // 设置默认立项决策日期为当前日期
            if (decisionDateInput) {
                const today = new Date();
                const year = today.getFullYear();
                const month = String(today.getMonth() + 1).padStart(2, '0');
                const day = String(today.getDate()).padStart(2, '0');
                decisionDateInput.value = `${year}-${month}-${day}`;
            }

            // 招标类别级联
            if (procurementTypeSelect && biddingCategorySelect) {
                const biddingCategories = {
                    '施工': ['工程类(施工/勘察/EPC/监理)'],
                    '货物': ['货物类(材料/设备/供应及安装)'],
                    '服务': ['服务类(监理/咨询/物业)'],
                    '其他': ['其他(文本输入)']
                };

                const updateBiddingCategory = () => {
                    const selectedType = procurementTypeSelect.value;
                    const categories = biddingCategories[selectedType] || [];
                    biddingCategorySelect.innerHTML = '';
                    categories.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category;
                        option.textContent = category;
                        biddingCategorySelect.appendChild(option);
                    });
                };

                // 初始加载
                updateBiddingCategory();
                procurementTypeSelect.addEventListener('change', updateBiddingCategory);
            }

            // 代理机构显示/隐藏
            if (procurementOrganizationMethodSelect && agencyRow) {
                const toggleAgencyVisibility = () => {
                    if (procurementOrganizationMethodSelect.value === '委托招标') {
                        agencyRow.style.display = 'flex';
                        document.getElementById('agency').required = true;
                    } else {
                        agencyRow.style.display = 'none';
                        document.getElementById('agency').required = false;
                    }
                };

                // 初始加载
                toggleAgencyVisibility();
                procurementOrganizationMethodSelect.addEventListener('change', toggleAgencyVisibility);
            }

            // 设置默认招标时间
            const biddingTimeInput = document.getElementById('biddingTime');
            if (biddingTimeInput && !biddingTimeInput.value) {
                const currentYear = new Date().getFullYear();
                biddingTimeInput.value = `${currentYear}年第3季度`;
            }

            // ==================== 编辑模式数据加载 ====================
            // 获取URL参数判断是否为编辑模式
            const hash = window.top ? window.top.location.hash : window.location.hash;
            const queryString = hash.split('?')[1] || '';
            const urlParams = new URLSearchParams(queryString);
            const id = urlParams.get('id');

            if (id) {
                loadFormData(id);
            }
        }

        // ==================== 兼容iframe环境的初始化 ====================
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializePage);
        } else {
            // DOM已经加载完成，立即初始化（适用于iframe环境）
            initializePage();
        }

        // ==================== 数据加载函数 ====================
        function loadFormData(id) {
            // 示例数据加载逻辑（实际使用时替换为真实的API调用）
            const dummyData = {
                planNumber: id,
                planProjectName: '2024年度办公用品采购计划',
                procurementType: '货物',
                biddingCategory: '货物类(材料/设备/供应及安装)',
                procurementMethod: '公告比选',
                budgetAmount: 150.00,
                fundSource: '自有资金',
                biddingTime: '2024年第3季度',
                procurementOrganizationMethod: '自主招标',
                agency: '',
                annualProcurementPlan: 140.00,
                projectHandler: '张三',
                decisionDate: '2024-08-08',
                projectType: '依法必须招标项目',
                projectOwner: '行政部',
                projectBasicInfo: '本计划旨在采购公司日常运营所需的各类办公用品，以确保各部门工作的顺利进行。',
                secondaryCompany: '集团本部',
                remarks: '请优先考虑环保型产品。'
            };

            // 填充表单数据
            Object.keys(dummyData).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = dummyData[key];
                }
            });

            // 触发相关的更新事件
            const procurementTypeSelect = document.getElementById('procurementType');
            const procurementOrganizationMethodSelect = document.getElementById('procurementOrganizationMethod');

            if (procurementTypeSelect) {
                procurementTypeSelect.dispatchEvent(new Event('change'));
            }
            if (procurementOrganizationMethodSelect) {
                procurementOrganizationMethodSelect.dispatchEvent(new Event('change'));
            }
        }
    </script>
</body>

</html>
