<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划管理 - 列表</title>
    <!-- CSS文件已在主页面(index.html)中统一加载，无需在模板中重复引入 -->
    <style>
        /*
        数据表格链接样式规范：
        1. 数据列可点击文本：使用 <span onclick="..." class="table-link"> 避免浏览器默认样式冲突
        2. 操作列链接：使用 <a> 标签但添加 :not(.table-link) 选择器
        3. 统一使用主题色变量确保一致性
        */
        .data-table .table-link {
            color: var(--funi-primary-color, #007FFF) !important;
            cursor: pointer !important;
            text-decoration: none !important;
            border: none !important;
            outline: none !important;
            font-weight: normal !important;
            display: inline !important;
            background: none !important;
            padding: 0 !important;
            margin: 0 !important;
        }

        .data-table .table-link:hover {
            color: var(--funi-primary-dark-color, #337ecc) !important;
            text-decoration: none !important;
        }

        .data-table .table-link:active,
        .data-table .table-link:focus {
            color: var(--funi-primary-color, #007FFF) !important;
            text-decoration: none !important;
            outline: none !important;
        }

        /* 操作列链接样式 - 仍使用a标签但确保主题色 */
        .data-table tbody td a:not(.table-link),
        .data-table td a:not(.table-link) {
            color: var(--funi-primary-color, #007FFF) !important;
            text-decoration: none !important;
            cursor: pointer !important;
        }
        .data-table tbody td a:not(.table-link):hover,
        .data-table td a:not(.table-link):hover {
            color: var(--funi-primary-dark-color, #337ecc) !important;
            text-decoration: none !important;
        }
    </style>
</head>

<body>
    <div id="app" class="container">
        <div class="container-header">
            <!-- 1. 头部Tab切换 -->
            <div class="tabs">
                <div class="tab-item active" data-tab="pending">待办</div>
                <div class="tab-item" data-tab="done">已办</div>
            </div>

            <!-- 2. 搜索区域 -->
            <div class="search-area collapsed">
                <form class="search-form">
                    <div class="search-form-item">
                        <label for="planProjectName">计划项目名称:</label>
                        <input type="text" id="planProjectName" name="planProjectName" placeholder="2位以上进行模糊查询">
                    </div>
                    <div class="search-form-item">
                        <label for="auditStatus">审核状态:</label>
                        <select id="auditStatus" name="auditStatus" multiple>
                            <option value="待审核">待审核</option>
                            <option value="审核中">审核中</option>
                            <option value="审核通过">审核通过</option>
                            <option value="审核未过">审核未过</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="procurementType">采购类型:</label>
                        <select id="procurementType" name="procurementType" multiple>
                            <option value="货物">货物</option>
                            <option value="施工">施工</option>
                            <option value="服务">服务</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="procurementMethod">采购方式:</label>
                        <select id="procurementMethod" name="procurementMethod" multiple>
                            <option value="公告比选">公告比选</option>
                            <option value="邀请比选">邀请比选</option>
                            <option value="竞争性磋商">竞争性磋商</option>
                            <option value="竞争性谈判">竞争性谈判</option>
                            <option value="询价择优">询价择优</option>
                            <option value="单一来源">单一来源</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="procurementOrganizationMethod">采购组织方式:</label>
                        <select id="procurementOrganizationMethod" name="procurementOrganizationMethod" multiple>
                            <option value="自主招标">自主招标</option>
                            <option value="委托招标">委托招标</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="fundSource">资金来源:</label>
                        <select id="fundSource" name="fundSource" multiple>
                            <option value="自有资金">自有资金</option>
                            <option value="政府资本">政府资本</option>
                            <option value="其他社会资本">其他社会资本</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="budgetAmountMin">采购预算金额(万元):</label>
                        <div class="range-input">
                            <input type="number" id="budgetAmountMin" name="budgetAmountMin" placeholder="最小">
                            <span>-</span>
                            <input type="number" id="budgetAmountMax" name="budgetAmountMax" placeholder="最大">
                        </div>
                    </div>
                    <div class="search-form-item">
                        <label for="projectHandler">项目经办人:</label>
                        <input type="text" id="projectHandler" name="projectHandler" placeholder="2位以上进行模糊查询">
                    </div>
                    <div class="search-form-item">
                        <label for="decisionDateStart">立项决策日期:</label>
                        <div class="date-range-picker">
                            <input type="date" id="decisionDateStart" name="decisionDateStart">
                            <span>~</span>
                            <input type="date" id="decisionDateEnd" name="decisionDateEnd">
                        </div>
                    </div>
                    <div class="search-form-item full-width">
                        <label for="createTimeStart">创建时间:</label>
                        <div class="date-range-picker">
                            <input type="date" id="createTimeStart" name="createTimeStart">
                            <span>~</span>
                            <input type="date" id="createTimeEnd" name="createTimeEnd">
                        </div>
                    </div>
                    <div class="search-form-item search-buttons-item">
                        <button type="button" class="button primary" id="queryButton">查询</button>
                        <button type="button" class="button" id="resetButton">重置</button>
                        <button type="button" class="button text" id="toggleCollapseButton">高级查询</button>
                    </div>
                </form>
            </div>
        </div>
        <div class="container-table">
            <!-- 操作按钮区域 -->
            <div class="action-buttons">
                <button class="button primary" onclick="window.addNew()">新建</button>
            </div>
            <!-- 3. 列表区域 -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>计划编号</th>
                            <th>计划项目名称</th>
                            <th>采购类型</th>
                            <th>采购方式</th>
                            <th>审核状态</th>
                            <th>采购组织方式</th>
                            <th>代理机构</th>
                            <th>项目经办人</th>
                            <th>项目业主</th>
                            <th>招标时间</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- Table rows will be inserted here by JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- 分页组件 -->
            <div class="pagination-container">
                <span>总共 <span id="totalItems">0</span> 条</span>
                <select id="pageSizeSelect">
                    <option value="10">10 条/页</option>
                    <option value="20">20 条/页</option>
                    <option value="50">50 条/页</option>
                    <option value="100">100 条/页</option>
                </select>
                <div class="page-buttons">
                    <button id="prevPageButton" disabled>上一页</button>
                    <span id="currentPageSpan">1</span>
                    <button id="nextPageButton">下一页</button>
                </div>
            </div>
        </div>
    </div>
    <!-- JS文件已在主页面(index.html)中统一加载，无需在模板中重复引入 -->
    <script>
        // 全局导航函数 - 必须在页面加载前定义，确保onclick属性能正确访问
        window.viewDetail = function(id) {
            window.top.location.hash = `#/procurement-plan/detail-review?id=${id}`;
        };

        window.editItem = function(id) {
            window.top.location.hash = `#/procurement-plan/add-edit?id=${id}`;
        };

        window.addNew = function() {
            window.top.location.hash = `#/procurement-plan/add-edit`;
        };

        // 操作按钮处理函数 - 使用onclick属性确保iframe环境兼容性
        window.handleViewAction = function(id) {
            window.viewDetail(id);
        };

        window.handleEditAction = function(id) {
            window.editItem(id);
        };

        window.handleDeleteAction = function(id) {
            if (confirm(`确定要删除编号为 ${id} 的记录吗？`)) {
                alert(`删除功能 - ID: ${id}`);
                // 实际项目中这里应该调用删除API
            }
        };

        window.handleSubmitAction = function(id) {
            if (confirm(`确定要提交编号为 ${id} 的计划吗？`)) {
                alert(`提交功能 - ID: ${id}`);
                // 实际项目中这里应该调用提交API
            }
        };

        window.handleAuditAction = function(id) {
            window.top.location.hash = `#/procurement-plan/detail-review?id=${id}&review=1`;
        };

        window.handleRevokeAction = function(id) {
            if (confirm(`确定要撤销编号为 ${id} 的计划吗？`)) {
                alert(`撤销功能 - ID: ${id}`);
                // 实际项目中这里应该调用撤销API
            }
        };

        document.addEventListener('DOMContentLoaded', () => {
            const tabs = document.querySelectorAll('.tab-item');
            const searchForm = document.querySelector('.search-form');
            const queryButton = document.getElementById('queryButton');
            const resetButton = document.getElementById('resetButton');
            const toggleCollapseButton = document.getElementById('toggleCollapseButton');
            const tableBody = document.getElementById('tableBody');
            const pageSizeSelect = document.getElementById('pageSizeSelect');
            const prevPageButton = document.getElementById('prevPageButton');
            const nextPageButton = document.getElementById('nextPageButton');
            const currentPageSpan = document.getElementById('currentPageSpan');
            const totalItemsSpan = document.getElementById('totalItems');

            // 检查必需的元素是否存在
            if (!tableBody || !pageSizeSelect || !queryButton || !resetButton || !toggleCollapseButton) {
                console.error('Required elements not found');
                return;
            }

            let activeTab = 'pending';
            let isCollapsed = true;
            let currentPage = 1;
            let pageSize = parseInt(pageSizeSelect.value) || 10;

            // Initial state for the search area
            const searchArea = document.querySelector('.search-area');
            if (searchArea && toggleCollapseButton) {
                if (isCollapsed) {
                    searchArea.classList.add('collapsed');
                    toggleCollapseButton.textContent = '高级查询';
                } else {
                    searchArea.classList.remove('collapsed');
                    toggleCollapseButton.textContent = '收起';
                }
            }

            // 采购计划管理模拟数据
            const allTableData = [
                {
                    planNumber: 'CG—20240808—0001',
                    planProjectName: '2024年度办公用品采购计划',
                    procurementType: '货物',
                    procurementMethod: '公告比选',
                    auditStatus: '待审核',
                    procurementOrganizationMethod: '自主招标',
                    agency: '无',
                    projectHandler: '张三',
                    projectOwner: '行政部',
                    biddingTime: '2024年第三季度',
                    createTime: '2024-08-08'
                },
                {
                    planNumber: 'CG—20240807—0002',
                    planProjectName: '办公楼装修工程采购计划',
                    procurementType: '施工',
                    procurementMethod: '公告比选',
                    auditStatus: '审核中',
                    procurementOrganizationMethod: '委托招标',
                    agency: '代理机构A',
                    projectHandler: '李四',
                    projectOwner: '基建部',
                    biddingTime: '2024年第四季度',
                    createTime: '2024-08-07'
                },
                {
                    planNumber: 'CG—20240806—0003',
                    planProjectName: '信息系统维护服务采购计划',
                    procurementType: '服务',
                    procurementMethod: '竞争性磋商',
                    auditStatus: '审核通过',
                    procurementOrganizationMethod: '自主招标',
                    agency: '无',
                    projectHandler: '王五',
                    projectOwner: 'IT部',
                    biddingTime: '2024年第二季度',
                    createTime: '2024-08-06'
                },
                {
                    planNumber: 'CG—20240805—0004',
                    planProjectName: '车辆采购计划',
                    procurementType: '货物',
                    procurementMethod: '邀请比选',
                    auditStatus: '审核未过',
                    procurementOrganizationMethod: '委托招标',
                    agency: '代理机构B',
                    projectHandler: '赵六',
                    projectOwner: '总务部',
                    biddingTime: '2024年第一季度',
                    createTime: '2024-08-05'
                },
                {
                    planNumber: 'CG—20240804—0005',
                    planProjectName: '设备维修服务采购计划',
                    procurementType: '服务',
                    procurementMethod: '询价择优',
                    auditStatus: '待审核',
                    procurementOrganizationMethod: '自主招标',
                    agency: '无',
                    projectHandler: '钱七',
                    projectOwner: '设备部',
                    biddingTime: '2024年第三季度',
                    createTime: '2024-08-04'
                }
            ];

            let filteredTableData = [...allTableData];

            const renderTable = () => {
                tableBody.innerHTML = '';
                const start = (currentPage - 1) * pageSize;
                const end = start + pageSize;
                const paginatedData = filteredTableData.slice(start, end);

                paginatedData.forEach(rowData => {
                    const row = document.createElement('tr');

                    // 根据审核状态动态生成操作按钮
                    let actionButtons = '';
                    if (rowData.auditStatus === '待审核') {
                        actionButtons = `
                            <button type="button" class="button text" onclick="window.handleViewAction('${rowData.planNumber}')">详情</button>
                            <button type="button" class="button text" onclick="window.handleEditAction('${rowData.planNumber}')">编辑</button>
                            <button type="button" class="button text" onclick="window.handleDeleteAction('${rowData.planNumber}')">删除</button>
                            <button type="button" class="button text" onclick="window.handleSubmitAction('${rowData.planNumber}')">提交</button>
                        `;
                    } else if (rowData.auditStatus === '审核中') {
                        actionButtons = `
                            <button type="button" class="button text" onclick="window.handleViewAction('${rowData.planNumber}')">详情</button>
                            <button type="button" class="button text" onclick="window.handleAuditAction('${rowData.planNumber}')">审核</button>
                            <button type="button" class="button text" onclick="window.handleRevokeAction('${rowData.planNumber}')">撤销</button>
                        `;
                    } else {
                        actionButtons = `
                            <button type="button" class="button text" onclick="window.handleViewAction('${rowData.planNumber}')">详情</button>
                        `;
                    }

                    row.innerHTML = `
                        <td>${rowData.planNumber}</td>
                        <td><span onclick="window.viewDetail('${rowData.planNumber}')" class="table-link">${rowData.planProjectName}</span></td>
                        <td>${rowData.procurementType}</td>
                        <td>${rowData.procurementMethod}</td>
                        <td>${rowData.auditStatus}</td>
                        <td>${rowData.procurementOrganizationMethod}</td>
                        <td>${rowData.agency}</td>
                        <td>${rowData.projectHandler}</td>
                        <td>${rowData.projectOwner}</td>
                        <td>${rowData.biddingTime}</td>
                        <td>${rowData.createTime}</td>
                        <td>${actionButtons}</td>
                    `;
                    tableBody.appendChild(row);
                });

                totalItemsSpan.textContent = filteredTableData.length;
                currentPageSpan.textContent = currentPage;
                prevPageButton.disabled = currentPage === 1;
                nextPageButton.disabled = currentPage * pageSize >= filteredTableData.length;
            };

            // Event Listeners
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    tabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    activeTab = tab.dataset.tab;

                    // 根据tab过滤数据
                    if (activeTab === 'pending') {
                        filteredTableData = allTableData.filter(item =>
                            ['待审核', '审核中', '审核未过'].includes(item.auditStatus)
                        );
                    } else if (activeTab === 'done') {
                        filteredTableData = allTableData.filter(item =>
                            item.auditStatus === '审核通过'
                        );
                    }

                    currentPage = 1;
                    renderTable();
                });
            });

            queryButton.addEventListener('click', () => {
                const formData = new FormData(searchForm);
                const searchParams = {};
                for (let [key, value] of formData.entries()) {
                    searchParams[key] = value;
                }
                console.log('查询条件:', searchParams);
                // 实际项目中这里应该调用API进行查询
                currentPage = 1;
                renderTable();
            });

            resetButton.addEventListener('click', () => {
                searchForm.reset();
                console.log('重置搜索条件');
                // 重置过滤数据
                if (activeTab === 'pending') {
                    filteredTableData = allTableData.filter(item =>
                        ['待审核', '审核中', '审核未过'].includes(item.auditStatus)
                    );
                } else if (activeTab === 'done') {
                    filteredTableData = allTableData.filter(item =>
                        item.auditStatus === '审核通过'
                    );
                }
                currentPage = 1;
                renderTable();
            });

            toggleCollapseButton.addEventListener('click', () => {
                const searchArea = document.querySelector('.search-area');
                isCollapsed = !isCollapsed;
                if (isCollapsed) {
                    searchArea.classList.add('collapsed');
                    toggleCollapseButton.textContent = '高级查询';
                } else {
                    searchArea.classList.remove('collapsed');
                    toggleCollapseButton.textContent = '收起';
                }
            });

            pageSizeSelect.addEventListener('change', (event) => {
                pageSize = parseInt(event.target.value);
                currentPage = 1;
                renderTable();
            });

            prevPageButton.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    renderTable();
                }
            });

            nextPageButton.addEventListener('click', () => {
                const totalPages = Math.ceil(filteredTableData.length / pageSize);
                if (currentPage < totalPages) {
                    currentPage++;
                    renderTable();
                }
            });

            // 初始化时显示待办数据
            filteredTableData = allTableData.filter(item =>
                ['待审核', '审核中', '审核未过'].includes(item.auditStatus)
            );
            renderTable();
        });
    </script>
</body>

</html>
